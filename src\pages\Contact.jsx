import React, { useState } from "react";
import { Mail, Phone, MapPin, Clock, CheckCircle, Zap } from "lucide-react";

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    service: "",
    budget: "",
    message: "",
    timeline: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const services = [
    "Software Development",
    "Media Production",
    "Graphic Design",
    "Social Media Marketing",
    "Large Format Printing",
    "Voice Acting",
    "Music Studio Recording",
    "Full Service Package",
    "Not Sure - Need Consultation"
  ];

  const budgets = [
    "Under TSH 7,000,000/month",
    "TSH 7,000,000 - TSH 12,000,000/month",
    "TSH 12,000,000 - TSH 24,000,000/month",
    "TSH 24,000,000 - TSH 60,000,000/month",
    "TSH 60,000,000+/month",
    "Let's discuss"
  ];

  const timelines = [
    "ASAP - Need results yesterday",
    "Within 30 days",
    "1-3 months",
    "3-6 months",
    "Just exploring options"
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("Form submitted:", formData);
      setIsSubmitted(true);
    } catch (error) {
      console.error("Error sending email:", error);
      alert("There was an error sending your message. Please try again or contact us directly.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-green-400 flex items-center justify-center py-20">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 md:p-12 brutal-border brutal-shadow">
            <div className="bg-green-400 w-20 h-20 brutal-border brutal-shadow mx-auto mb-8 flex items-center justify-center">
              <CheckCircle className="w-10 h-10 text-black" />
            </div>
            <h1 className="brutal-text text-4xl mb-6">MESSAGE RECEIVED!</h1>
            <p className="text-xl font-bold mb-8">
              Thanks for reaching out! We'll review your information and get back to you within 24 hours with a custom strategy proposal.
            </p>
            <p className="font-bold">
              Keep an eye on your inbox - we're about to show you exactly how to turn your social media into a revenue machine.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-pink-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">LET'S TALK</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-white max-w-3xl mx-auto">
            Ready to transform your business with comprehensive technology solutions?
            Let's discuss your project and build a custom solution for your needs.
          </p>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <div className="bg-white brutal-border brutal-shadow p-8">
                <h2 className="brutal-text text-3xl mb-8">GET YOUR FREE CONSULTATION</h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="brutal-text text-sm mb-2 block">YOUR NAME *</label>
                      <input
                        type="text"
                        required
                        value={formData.name}
                        onChange={(e) => handleInputChange("name", e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        placeholder="Enter your full name"
                      />
                    </div>
                    
                    <div>
                      <label className="brutal-text text-sm mb-2 block">EMAIL *</label>
                      <input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="brutal-text text-sm mb-2 block">COMPANY *</label>
                      <input
                        type="text"
                        required
                        value={formData.company}
                        onChange={(e) => handleInputChange("company", e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        placeholder="Your company name"
                      />
                    </div>
                    
                    <div>
                      <label className="brutal-text text-sm mb-2 block">PHONE</label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        placeholder="+*********** 044"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="brutal-text text-sm mb-2 block">SERVICE INTEREST *</label>
                    <select 
                      value={formData.service} 
                      onChange={(e) => handleInputChange("service", e.target.value)}
                      className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                      required
                    >
                      <option value="">What service are you interested in?</option>
                      {services.map((service) => (
                        <option key={service} value={service}>{service}</option>
                      ))}
                    </select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="brutal-text text-sm mb-2 block">BUDGET RANGE *</label>
                      <select 
                        value={formData.budget} 
                        onChange={(e) => handleInputChange("budget", e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        required
                      >
                        <option value="">Select your budget range</option>
                        {budgets.map((budget) => (
                          <option key={budget} value={budget}>{budget}</option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="brutal-text text-sm mb-2 block">TIMELINE *</label>
                      <select 
                        value={formData.timeline} 
                        onChange={(e) => handleInputChange("timeline", e.target.value)}
                        className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                        required
                      >
                        <option value="">When do you want to start?</option>
                        {timelines.map((timeline) => (
                          <option key={timeline} value={timeline}>{timeline}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="brutal-text text-sm mb-2 block">TELL US ABOUT YOUR GOALS</label>
                    <textarea
                      value={formData.message}
                      onChange={(e) => handleInputChange("message", e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150 resize-none"
                      placeholder="What are your main business goals? What challenges are you facing with social media? The more details you provide, the better we can help."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 disabled:opacity-50 inline-flex items-center justify-center"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        SENDING...
                      </>
                    ) : (
                      <>
                        <Zap className="w-5 h-5 mr-2" fill="currentColor" />
                        BOOK MY FREE CONSULTATION
                      </>
                    )}
                  </button>
                </form>
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow asymmetric-grid">
                <h3 className="brutal-text text-xl mb-4">CONTACT INFO</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5" />
                    <span className="font-bold"><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5" />
                    <span className="font-bold">+*********** 044</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5" />
                    <span className="font-bold">41220 Nzuguni<br />Dodoma, Tanzania</span>
                  </div>
                </div>
              </div>

              <div className="bg-blue-500 text-white p-6 brutal-border brutal-shadow anti-asymmetric">
                <h3 className="brutal-text text-xl mb-4">RESPONSE TIME</h3>
                <div className="flex items-center gap-3 mb-4">
                  <Clock className="w-5 h-5" />
                  <span className="font-bold">Within 24 hours</span>
                </div>
                <p className="font-bold text-sm">
                  We respond to all inquiries within 24 hours. For urgent matters, call us directly.
                </p>
              </div>

              <div className="bg-green-400 text-black p-6 brutal-border brutal-shadow">
                <h3 className="brutal-text text-xl mb-4">WHAT HAPPENS NEXT?</h3>
                <div className="space-y-3 text-sm font-bold">
                  <div className="flex items-start gap-3">
                    <span className="bg-black text-white w-6 h-6 brutal-text text-xs flex items-center justify-center mt-1">1</span>
                    <span>We review your information and goals</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-black text-white w-6 h-6 brutal-text text-xs flex items-center justify-center mt-1">2</span>
                    <span>We prepare a custom strategy proposal</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-black text-white w-6 h-6 brutal-text text-xs flex items-center justify-center mt-1">3</span>
                    <span>We schedule your free strategy call</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
